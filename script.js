const boardSize = 3;
let board = Array.from({ length: boardSize }, () =>
  Array.from({ length: boardSize }, () => [])
);
let currentPlayer = 'X';

const boardDiv = document.getElementById('board');
const statusDiv = document.getElementById('status');

function renderBoard() {
  boardDiv.innerHTML = '';
  for (let i = 0; i < boardSize; i++) {
    for (let j = 0; j < boardSize; j++) {
      const cell = document.createElement('div');
      cell.className = 'cell';
      const stack = board[i][j];
      if (stack.length > 0) {
        const top = stack[stack.length - 1];
        cell.textContent = top.player + `(${top.size})`;
      }
      cell.onclick = () => handleMove(i, j);
      boardDiv.appendChild(cell);
    }
  }
}
function handleMove(i, j) {
  const sizeOptions = ['small', 'medium', 'large'];
  const size = prompt("เลือกขนาดหมาก: small / medium / large");

  if (!sizeOptions.includes(size)) return alert("ขนาดไม่ถูกต้อง");

  const stack = board[i][j];
  if (
    stack.length === 0 ||
    isBigger(size, stack[stack.length - 1].size)
  ) {
    stack.push({ player: currentPlayer, size });
    currentPlayer = currentPlayer === 'X' ? 'O' : 'X';
    statusDiv.textContent = `Player ${currentPlayer}'s turn`;
    renderBoard();
  } else {
    alert("หมากเล็กกว่าหรือเท่ากับหมากที่อยู่บนสุด!");
  }
}

function isBigger(size1, size2) {
  const order = { small: 1, medium: 2, large: 3 };
  return order[size1] > order[size2];
}
